import { useState, useEffect } from 'react';
import { useLocation, useParams } from 'wouter';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { format } from 'date-fns';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Check, ChevronLeft, ChevronDown, ChevronUp, X, Calendar as CalendarIcon,
  ListChecks, Users, ImageIcon, Presentation, Search, FileVideo, FileImage,
  FolderSearch, ListOrdered, Layers, FileQuestion, ArrowUp, ArrowDown,
  Trash2, Monitor, MonitorX, CheckCircle2, MonitorOff, MonitorSmartphone,
  Loader2, Plus, Filter, Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { MainLayout } from '@/components/layout/main-layout';
import { apiRequest } from '@/lib/queryClient';
import type {
  Campaign,
  InsertCampaign,
  Screen,
  CampaignMedia,
  CampaignScreen
} from '@shared/schema';

// Define interfaces since they're not available in schema
interface Media {
  id: string;
  name: string;
  type?: string;
  displayOrder?: number;
  [key: string]: any;
}

interface Slide {
  id: string;
  name: string;
  type?: string;
  displayOrder?: number;
  slideConfig?: any;
  [key: string]: any;
}

// Form validation schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  startDate: z.date({
    required_error: "Start date is required",
  }),
  endDate: z.date({
    required_error: "End date is required",
  }).refine(
    (date) => date instanceof Date,
    {
      message: "End date is required",
    }
  ),
  status: z.string().optional(),
  description: z.string().optional(),
  triggerType: z.number().int().min(1).max(4).default(1),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  radius: z.number().positive().optional(),
  minTemp: z.number().optional(),
  maxTemp: z.number().optional(),
  units: z.enum(["standard", "metric", "imperial"]).optional(),
  distance: z.number().positive().optional(),
});

export default function CampaignDetailsPage() {
  const { id } = useParams();
  const [location, navigate] = useLocation();
  const isNewCampaign = id === 'new';

  // Validate campaign ID format (should be UUID or 'new')
  const isValidId = isNewCampaign || (id && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));

  // Redirect to campaigns list if invalid ID
  useEffect(() => {
    if (!isValidId) {
      console.error('Invalid campaign ID:', id);
      navigate('/campaigns');
    }
  }, [id, isValidId, navigate]);
  const [activeTab, setActiveTab] = useState('details');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [campaign, setCampaign] = useState<Campaign | null>(null);

  const [selectedMedia, setSelectedMedia] = useState<Array<Media | Slide>>([]);
  const [selectedScreens, setSelectedScreens] = useState<Screen[]>([]);

  // Store all available items (complete lists)
  const [allMediaItems, setAllMediaItems] = useState<Media[]>([]);
  const [allSlides, setAllSlides] = useState<Slide[]>([]);
  const [allScreens, setAllScreens] = useState<Screen[]>([]);

  // Store filtered available items (what's shown in the UI)
  const [mediaItems, setMediaItems] = useState<Media[]>([]);
  const [slides, setSlides] = useState<Slide[]>([]);
  const [screens, setScreens] = useState<Screen[]>([]);

  const [mediaSearchQuery, setMediaSearchQuery] = useState('');
  const [screenSearchQuery, setScreenSearchQuery] = useState('');
  const [selectedMediaTag, setSelectedMediaTag] = useState('');
  const [selectedScreenTag, setSelectedScreenTag] = useState('');
  const [selectedScreenStatus, setSelectedScreenStatus] = useState('');
  const [mediaTags, setMediaTags] = useState<any[]>([]);
  const [screenTags, setScreenTags] = useState<any[]>([]);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Form setup
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      startDate: new Date(),
      endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
      status: 'scheduled',
      description: '',
      triggerType: 1,
      latitude: undefined,
      longitude: undefined,
      radius: undefined,
      minTemp: undefined,
      maxTemp: undefined,
      units: undefined,
      distance: undefined,
    }
  });

  // Get the current user and their teams
  const { user } = useAuth();
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    enabled: !!user?.id,
  });
  const [teamId, setTeamId] = useState<string>("");

  // Set teamId when teams are loaded
  useEffect(() => {
    if (teams && teams.length > 0) {
      setTeamId(teams[0].id);
    }
  }, [teams]);

  // Function to update available content lists by filtering out selected items
  const updateAvailableContent = () => {
    if (!teamId) return;

    console.log("Updating available content based on selections");

    // Get all media IDs that are already selected
    const selectedMediaIds = selectedMedia.map(item => item.id);
    console.log(`Selected media/slides: ${selectedMediaIds.length} items:`, selectedMediaIds);

    // Get all screen IDs that are already selected
    const selectedScreenIds = selectedScreens.map(screen => screen.id);
    console.log(`Selected screens: ${selectedScreenIds.length} screens:`, selectedScreenIds);

    // Update available media - filter out selected items from allMediaItems
    if (allMediaItems.length > 0) {
      // First reset the mediaItems to all available items
      const availableMedia = [...allMediaItems];

      // Then filter out the selected items
      const filteredAvailableMedia = availableMedia.filter(media =>
        !selectedMediaIds.includes(media.id)
      );

      console.log(`Filtered available media: ${filteredAvailableMedia.length} items (from ${allMediaItems.length} total)`);
      setMediaItems(filteredAvailableMedia);
    }

    // Update available slides - filter out selected items from allSlides
    if (allSlides.length > 0) {
      // First reset the slides to all available slides
      const availableSlides = [...allSlides];

      // Then filter out the selected slides
      const filteredAvailableSlides = availableSlides.filter(slide =>
        !selectedMediaIds.includes(slide.id)
      );

      console.log(`Filtered available slides: ${filteredAvailableSlides.length} items (from ${allSlides.length} total)`);
      setSlides(filteredAvailableSlides);
    }

    // Update available screens - filter out selected items from allScreens
    if (allScreens.length > 0) {
      // First reset the screens to all available screens
      const availableScreens = [...allScreens];

      // Then filter out the selected screens
      const filteredAvailableScreens = availableScreens.filter(screen =>
        !selectedScreenIds.includes(screen.id)
      );

      console.log(`Filtered available screens: ${filteredAvailableScreens.length} items (from ${allScreens.length} total)`);
      setScreens(filteredAvailableScreens);
    }
  };

  // Update available content whenever selected items change
  useEffect(() => {
    // Only filter content if we have full data to work with
    if (
      teamId &&
      !loadingData &&
      allMediaItems.length > 0 &&
      allSlides.length > 0 &&
      allScreens.length > 0
    ) {
      console.log("Running updateAvailableContent due to changes in selection lists");
      updateAvailableContent();
    }
  }, [selectedMedia, selectedScreens]);

  useEffect(() => {
    if (!teamId) {
      // Don't show error immediately, wait for teams to load
      if (teams && teams.length === 0) {
        toast({
          title: "Error",
          description: "Team ID not found. Please return to the dashboard and select a team.",
          variant: "destructive"
        });
      }
      return;
    }

    // Load campaign data if editing
    const loadCampaign = async () => {
      if (!isNewCampaign) {
        try {
          // First load campaign basic data
          const response = await apiRequest('GET', `/api/campaigns/${id}`);
          const campaignData = await response.json();

          console.log('🔍 Campaign data received from API:', campaignData);
          console.log('🔍 Trigger type fields:', {
            triggerType: campaignData.triggerType,
            latitude: campaignData.latitude,
            longitude: campaignData.longitude,
            radius: campaignData.radius,
            minTemp: campaignData.minTemp,
            maxTemp: campaignData.maxTemp,
            units: campaignData.units,
            distance: campaignData.distance
          });

          setCampaign(campaignData);

          // Update form values
          form.setValue('name', campaignData.name);
          form.setValue('startDate', campaignData.startDate ? new Date(campaignData.startDate) : undefined);
          form.setValue('endDate', campaignData.endDate ? new Date(campaignData.endDate) : undefined);
          form.setValue('status', campaignData.status || 'scheduled');
          form.setValue('description', campaignData.description || '');

          form.setValue('triggerType', campaignData.triggerType || 1);
          form.setValue('latitude', campaignData.latitude ? parseFloat(campaignData.latitude) : undefined);
          form.setValue('longitude', campaignData.longitude ? parseFloat(campaignData.longitude) : undefined);
          form.setValue('radius', campaignData.radius ? parseFloat(campaignData.radius) : undefined);
          form.setValue('minTemp', campaignData.minTemp ? parseFloat(campaignData.minTemp) : undefined);
          form.setValue('maxTemp', campaignData.maxTemp ? parseFloat(campaignData.maxTemp) : undefined);
          form.setValue('units', campaignData.units || undefined);
          form.setValue('distance', campaignData.distance ? parseFloat(campaignData.distance) : undefined);

          // Load campaign media with expanded details
          const mediaResponse = await apiRequest('GET', `/api/campaigns/${id}/media`);
          const campaignMedia = await mediaResponse.json();
          console.log("Campaign media details:", campaignMedia);

          // Load campaign screens with expanded details
          const screensResponse = await apiRequest('GET', `/api/campaigns/${id}/screens`);
          const campaignScreens = await screensResponse.json();
          console.log("Campaign screens details:", campaignScreens);

          // Set selected media/slides based on loaded data
          // Note: mediaItems, slides, and screens should already be loaded
          const mediaAndSlides: Array<Media | Slide> = [];

          console.log("Campaign media:", campaignMedia);
          console.log("Available media items:", mediaItems);
          console.log("Available slides:", slides);

          console.log("Loading media and slides using:", {
            allMediaItems: allMediaItems.map(m => ({ id: m.id, name: m.name })),
            allSlides: allSlides.map(s => ({ id: s.id, name: s.name }))
          });

          // First collect all IDs of selected items for tracking
          const selectedMediaIds = new Set();

          for (const item of campaignMedia) {
            console.log(`Processing campaign item: ${JSON.stringify({
              mediaId: item.mediaId,
              type: item.campaignType,
              order: item.displayOrder
            })}`);

            // Keep track of which IDs are selected
            selectedMediaIds.add(item.mediaId);

            if (item.campaignType === 0) {
              // Media file
              if (item.media) {
                // Use the media from the join if available
                const mediaItem = {
                  ...item.media,
                  displayOrder: item.displayOrder
                };
                mediaAndSlides.push(mediaItem);
                console.log(`Added media directly from response: ${item.media.name} (ID: ${item.media.id})`);
              } else {
                // Try to find media in our complete media list
                const mediaId = item.mediaId;
                const mediaItem = allMediaItems.find(m => m.id === mediaId);

                if (mediaItem) {
                  mediaAndSlides.push({
                    ...mediaItem,
                    displayOrder: item.displayOrder
                  });
                  console.log(`Added media from complete list: ${mediaItem.name} (ID: ${mediaItem.id})`);
                } else {
                  // Last resort - fetch the individual media item
                  console.warn(`Media with ID ${mediaId} not found in loaded media, attempting to fetch`);
                  try {
                    const mediaResponse = await apiRequest('GET', `/api/media/${mediaId}`);
                    const mediaData = await mediaResponse.json();
                    mediaAndSlides.push({
                      ...mediaData,
                      displayOrder: item.displayOrder
                    });
                    console.log(`Fetched and added media: ${mediaData.name} (ID: ${mediaData.id})`);
                  } catch (mediaError) {
                    console.error(`Failed to fetch media ${mediaId}:`, mediaError);
                  }
                }
              }
            } else {
              // Slide
              if (item.slide) {
                // Use the slide from the join if available
                const slideItem = {
                  ...item.slide,
                  displayOrder: item.displayOrder,
                  type: 'slide'
                };
                mediaAndSlides.push(slideItem);
                console.log(`Added slide directly from response: ${item.slide.name} (ID: ${item.slide.id})`);
              } else {
                // Try to find slide in our complete slides list
                const slideId = item.mediaId;
                const slideItem = allSlides.find(s => s.id === slideId);

                if (slideItem) {
                  mediaAndSlides.push({
                    ...slideItem,
                    displayOrder: item.displayOrder,
                    type: 'slide'
                  });
                  console.log(`Added slide from complete list: ${slideItem.name} (ID: ${slideItem.id})`);
                } else {
                  // Last resort - fetch the individual slide
                  console.warn(`Slide with ID ${slideId} not found in loaded slides, attempting to fetch`);
                  try {
                    const slideResponse = await apiRequest('GET', `/api/slides/${slideId}`);
                    const slideData = await slideResponse.json();
                    mediaAndSlides.push({
                      ...slideData,
                      displayOrder: item.displayOrder,
                      type: 'slide'
                    });
                    console.log(`Fetched and added slide: ${slideData.name} (ID: ${slideData.id})`);
                  } catch (slideError) {
                    console.error(`Failed to fetch slide ${slideId}:`, slideError);
                  }
                }
              }
            }
          }

          // Immediately update available media and slides based on selections
          console.log("Filtering available media based on selected IDs:", Array.from(selectedMediaIds));

          // Update available media
          const filteredMedia = allMediaItems.filter(media => !selectedMediaIds.has(media.id));
          setMediaItems(filteredMedia);
          console.log(`Filtered out ${allMediaItems.length - filteredMedia.length} selected media items from available list`);

          // Update available slides
          const filteredSlides = allSlides.filter(slide => !selectedMediaIds.has(slide.id));
          setSlides(filteredSlides);
          console.log(`Filtered out ${allSlides.length - filteredSlides.length} selected slides from available list`);

          // Sort by display order
          mediaAndSlides.sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
          setSelectedMedia(mediaAndSlides);

          console.log("Campaign screens:", campaignScreens);
          console.log("Available screens:", screens);

          console.log("Loading screens using:", {
            allScreens: allScreens.map(s => ({ id: s.id, name: s.name }))
          });

          // First collect all screen IDs from selected screens
          const selectedScreenIds = new Set();

          // First try to get the screens that exist in the campaign
          let screenList = [];
          const screenIdsWithTagsToFetch = [];

          for (const item of campaignScreens) {
            // Keep track of which screen IDs are selected
            selectedScreenIds.add(item.screenId);
            screenIdsWithTagsToFetch.push(item.screenId);

            if (item.screen) {
              // Use screen from the joined data if available
              screenList.push({...item.screen, screenId: item.screenId});
              console.log(`Added screen directly from response: ${item.screen.name} (ID: ${item.screen.id})`);
            } else {
              // Try to find screen in our complete screens list
              const screenId = item.screenId;
              const screenItem = allScreens.find(s => s.id === screenId);

              if (screenItem) {
                screenList.push(screenItem);
                console.log(`Added screen from complete list: ${screenItem.name} (ID: ${screenItem.id})`);
              } else {
                // Last resort - fetch the individual screen
                console.warn(`Screen with ID ${screenId} not found in loaded screens, attempting to fetch`);
                try {
                  const screenResponse = await apiRequest('GET', `/api/screens/${screenId}`);
                  const screenData = await screenResponse.json();
                  screenList.push(screenData);
                  console.log(`Fetched and added screen: ${screenData.name} (ID: ${screenData.id})`);
                } catch (screenError) {
                  console.error(`Failed to fetch screen ${screenId}:`, screenError);
                }
              }
            }
          }

          console.log("Initial screen list before adding tags:", screenList.map(s => s.name));

          // CRITICAL FIX: Use the exact same pattern for fetching tags as we use for Available screens
          // The Available screens fetch works properly - we'll match it here for consistency

          console.log("🔄 REBUILDING SELECTED SCREENS WITH SAME TAG PATTERN AS AVAILABLE SCREENS");

          // Use the exact same Promise.all pattern that works for Available screens
          const selectedScreensWithTags = await Promise.all(screenList.map(async (screen) => {
            try {
              // This is the exact same pattern used for loading Available screens tags in loadAllScreens()
              const tagsResponse = await apiRequest('GET', `/api/screens/${screen.id}/tags`);
              const tags = await tagsResponse.json();

              if (tags && tags.length > 0) {
                console.log(`✅ SUCCESS: Fetched ${tags.length} tags for selected screen ${screen.name}:`,
                  tags.map((t: any) => t.name).join(", "));
              }

              // This is exactly how we add tags to available screens
              return { ...screen, tags };
            } catch (error) {
              console.error(`❌ ERROR: Failed to fetch tags for screen ${screen.id}:`, error);
              return { ...screen, tags: [] };
            }
          }));

          // Log complete debug information for the newly built list
          console.log("=== SELECTED SCREENS LIST WITH TAGS ===");
          selectedScreensWithTags.forEach(s => {
            console.log(`Screen ${s.name} (${s.id}): ${s.tags ? s.tags.length : 0} tags:`,
              s.tags && s.tags.length > 0 ? s.tags.map((t: any) => t.name).join(", ") : "none");
          });
          console.log("=================================");

          // Use the verified screen list with tags
          screenList = selectedScreensWithTags;

          // Immediately update available screens based on selections
          console.log("Filtering available screens based on selected IDs:", Array.from(selectedScreenIds));

          // Update available screens
          const filteredScreens = allScreens.filter(screen => !selectedScreenIds.has(screen.id));
          setScreens(filteredScreens);
          console.log(`Filtered out ${allScreens.length - filteredScreens.length} selected screens from available list`);

          setSelectedScreens(screenList);
          console.log(`Set ${screenList.length} selected screens from campaign data`);

        } catch (error) {
          console.error("Error loading campaign:", error);
          toast({
            title: "Error",
            description: "Failed to load campaign details.",
            variant: "destructive"
          });
        }
      }
    };

    // Load all media items with their tags using optimized endpoint
    const loadAllMedia = async () => {
      try {
        // Use the optimized endpoint that fetches media with tags in a single query
        const response = await apiRequest('GET', `/api/teams/${teamId}/media-with-tags`);
        const mediaWithTags = await response.json();

        if (!Array.isArray(mediaWithTags)) {
          console.error("Invalid media data received");
          return [];
        }

        // Store all media items
        setAllMediaItems(mediaWithTags);
        console.log(`Loaded ${mediaWithTags.length} media items from the server`);

        // Return data for direct use
        return mediaWithTags;
      } catch (error) {
        console.error("Error loading media:", error);
        toast({
          title: "Error",
          description: "Failed to load media items.",
          variant: "destructive"
        });
        return [];
      }
    };

    // Load all slides
    const loadAllSlides = async () => {
      try {
        const response = await apiRequest('GET', `/api/teams/${teamId}/slides`);
        const data = await response.json();

        // Store complete list of slides
        setAllSlides(data);
        console.log(`Loaded ${data.length} slides from the server`);

        // Return data for direct use
        return data;
      } catch (error) {
        console.error("Error loading slides:", error);
        toast({
          title: "Error",
          description: "Failed to load slides.",
          variant: "destructive"
        });
        return [];
      }
    };

    // Load all screens with their tags using optimized endpoint
    const loadAllScreens = async () => {
      try {
        // Use the optimized endpoint that fetches screens with tags in a single query
        const response = await apiRequest('GET', `/api/teams/${teamId}/screens-with-tags`);
        const screensWithTags = await response.json();

        if (!Array.isArray(screensWithTags)) {
          console.error("Invalid screens data received");
          return [];
        }

        // Store complete list of screens
        setAllScreens(screensWithTags);
        console.log(`Loaded ${screensWithTags.length} screens from the server`);

        // Return data for direct use
        return screensWithTags;
      } catch (error) {
        console.error("Error loading screens:", error);
        toast({
          title: "Error",
          description: "Failed to load screens.",
          variant: "destructive"
        });
        return [];
      }
    };

    // Load tags for filtering with media and screen relationships
    const loadTags = async () => {
      try {
        // First load all basic tags
        const response = await apiRequest('GET', `/api/teams/${teamId}/tags`);
        const tags = await response.json();

        // Wait for media and screens to be loaded before processing tags
        if (mediaItems.length > 0 && screens.length > 0) {
          // Process tags to add media and screen relationships
          const processedTags = tags.map((tag: any) => {
            // Find all media with this tag
            const mediaWithTag = mediaItems.filter(media =>
              media.tags && media.tags.some((t: any) => t.id === tag.id)
            );

            // Find all screens with this tag
            const screensWithTag = screens.filter(screen =>
              screen.tags && screen.tags.some((t: any) => t.id === tag.id)
            );

            return {
              ...tag,
              media: mediaWithTag.map(m => m.id),
              screens: screensWithTag.map(s => s.id)
            };
          });

          setMediaTags(processedTags);
          setScreenTags(processedTags);
        } else {
          // Just set the basic tags if we don't have media/screens yet
          setMediaTags(tags);
          setScreenTags(tags);
        }
      } catch (error) {
        console.error("Error loading tags:", error);
      }
    };

    // Sequence the loading operations to avoid update cycles
    const loadAllData = async () => {
      if (teamId) {
        setLoadingData(true);
        try {
          // Store the fetched data locally before setting state
          const mediaDataPromise = loadAllMedia();
          const slidesDataPromise = loadAllSlides();
          const screensDataPromise = loadAllScreens();

          // Wait for all API calls to complete
          const [mediaData, slidesData, screensData] = await Promise.all([
            mediaDataPromise,
            slidesDataPromise,
            screensDataPromise
          ]);

          // We don't immediately set state for available items yet, since we need
          // to filter based on selections first

          if (isNewCampaign) {
            // For new campaigns, all items should be available
            setMediaItems(mediaData);
            setSlides(slidesData);
            setScreens(screensData);
          } else {
            // For existing campaigns, we need to load campaign data first
            // to determine which items are selected
            try {
              // First load campaign basic data
              const response = await apiRequest('GET', `/api/campaigns/${id}`);
              const campaignData = await response.json();
              setCampaign(campaignData);

              // Update form values
              form.setValue('name', campaignData.name);
              form.setValue('startDate', campaignData.startDate ? new Date(campaignData.startDate) : undefined);
              form.setValue('endDate', campaignData.endDate ? new Date(campaignData.endDate) : undefined);
              form.setValue('status', campaignData.status || 'scheduled');
              form.setValue('description', campaignData.description || '');
              form.setValue('triggerType', campaignData.triggerType || 1);
              form.setValue('latitude', campaignData.latitude ? parseFloat(campaignData.latitude) : undefined);
              form.setValue('longitude', campaignData.longitude ? parseFloat(campaignData.longitude) : undefined);
              form.setValue('radius', campaignData.radius ? parseFloat(campaignData.radius) : undefined);
              form.setValue('minTemp', campaignData.minTemp ? parseFloat(campaignData.minTemp) : undefined);
              form.setValue('maxTemp', campaignData.maxTemp ? parseFloat(campaignData.maxTemp) : undefined);
              form.setValue('units', campaignData.units || undefined);
              form.setValue('distance', campaignData.distance ? parseFloat(campaignData.distance) : undefined);

              // Load campaign media with expanded details
              const mediaResponse = await apiRequest('GET', `/api/campaigns/${id}/media`);
              const campaignMedia = await mediaResponse.json();
              console.log("Campaign media details:", campaignMedia);

              // Load campaign screens with expanded details
              const screensResponse = await apiRequest('GET', `/api/campaigns/${id}/screens`);
              const campaignScreens = await screensResponse.json();
              console.log("Campaign screens details:", campaignScreens);

              // Process selected media/slides
              const mediaAndSlides: Array<Media | Slide> = [];
              const selectedMediaIds = new Set<string>();

              // Process each campaign media item
              for (const item of campaignMedia) {
                // Track selected IDs for filtering
                selectedMediaIds.add(item.mediaId);

                if (item.campaignType === 0) {
                  // Media file
                  if (item.media) {
                    // Use the media from the join if available
                    mediaAndSlides.push({
                      ...item.media,
                      displayOrder: item.displayOrder
                    });
                  } else {
                    // Find media in our local data or fetch it
                    const mediaItem = mediaData.find(m => m.id === item.mediaId);
                    if (mediaItem) {
                      mediaAndSlides.push({
                        ...mediaItem,
                        displayOrder: item.displayOrder
                      });
                    } else {
                      // Last resort - fetch individual item
                      try {
                        const res = await apiRequest('GET', `/api/media/${item.mediaId}`);
                        const data = await res.json();
                        mediaAndSlides.push({
                          ...data,
                          displayOrder: item.displayOrder
                        });
                      } catch (error) {
                        console.error(`Failed to fetch media ${item.mediaId}:`, error);
                      }
                    }
                  }
                } else {
                  // Slide
                  if (item.slide) {
                    // Use the slide from the join if available
                    mediaAndSlides.push({
                      ...item.slide,
                      displayOrder: item.displayOrder,
                      type: 'slide'
                    });
                  } else {
                    // Find slide in our local data or fetch it
                    const slideItem = slidesData.find(s => s.id === item.mediaId);
                    if (slideItem) {
                      mediaAndSlides.push({
                        ...slideItem,
                        displayOrder: item.displayOrder,
                        type: 'slide'
                      });
                    } else {
                      // Last resort - fetch individual item
                      try {
                        const res = await apiRequest('GET', `/api/slides/${item.mediaId}`);
                        const data = await res.json();
                        mediaAndSlides.push({
                          ...data,
                          displayOrder: item.displayOrder,
                          type: 'slide'
                        });
                      } catch (error) {
                        console.error(`Failed to fetch slide ${item.mediaId}:`, error);
                      }
                    }
                  }
                }
              }

              // Sort selected media by display order
              mediaAndSlides.sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));

              // Process selected screens
              const screenList: Screen[] = [];
              const selectedScreenIds = new Set<string>();

              // Process each campaign screen
              for (const item of campaignScreens) {
                // Track selected screen IDs for filtering
                selectedScreenIds.add(item.screenId);

                if (item.screen) {
                  // Use the screen from the join
                  screenList.push(item.screen);
                } else {
                  // Find screen in our local data or fetch it
                  const screenItem = screensData.find(s => s.id === item.screenId);
                  if (screenItem) {
                    screenList.push(screenItem);
                  } else {
                    // Last resort - fetch individual item
                    try {
                      const res = await apiRequest('GET', `/api/screens/${item.screenId}`);
                      const data = await res.json();
                      screenList.push(data);
                    } catch (error) {
                      console.error(`Failed to fetch screen ${item.screenId}:`, error);
                    }
                  }
                }
              }

              // Filter available items to remove selected ones
              const availableMedia = mediaData.filter(media => !selectedMediaIds.has(media.id));
              const availableSlides = slidesData.filter(slide => !selectedMediaIds.has(slide.id));
              const availableScreens = screensData.filter(screen => !selectedScreenIds.has(screen.id));

              console.log(`Filtered: ${availableMedia.length} media available (from ${mediaData.length})`);
              console.log(`Filtered: ${availableSlides.length} slides available (from ${slidesData.length})`);
              console.log(`Filtered: ${availableScreens.length} screens available (from ${screensData.length})`);

              // Set all state at once
              setSelectedMedia(mediaAndSlides);
              setSelectedScreens(screenList);
              setMediaItems(availableMedia);
              setSlides(availableSlides);
              setScreens(availableScreens);
            } catch (error) {
              console.error("Error loading campaign:", error);
              // Still set available items if campaign loading fails
              setMediaItems(mediaData);
              setSlides(slidesData);
              setScreens(screensData);
            }
          }

          // Finally load and process tags after media and screens are ready
          await loadTags();

        } catch (error) {
          console.error("Error loading data:", error);
        } finally {
          setLoadingData(false);
        }
      }
    };

    loadAllData();
  }, [id, teamId, isNewCampaign, teams]);

  const handleSaveCampaign = async (values: z.infer<typeof formSchema>) => {
    if (!teamId) {
      toast({
        title: "Error",
        description: "Team ID not found.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsLoading(true);

      // 1. Insert or update campaign
      const campaignData: InsertCampaign = {
        ...values,
        teamId
      };

      let savedCampaignId: string;

      if (isNewCampaign) {
        // Create new campaign
        const response = await apiRequest('POST', '/api/campaigns', campaignData);
        const newCampaignResponse = await response.json();
        const newCampaign = newCampaignResponse;
        savedCampaignId = newCampaign.id;
      } else {
        // Update existing campaign
        await apiRequest('PATCH', `/api/campaigns/${id}`, campaignData);
        savedCampaignId = id as string;
      }

      // 2. Delete existing media/slides and screens
      if (!isNewCampaign) {
        console.log("Step 1: Removing existing media/slides from campaign");
        try {
          // Get current media and slide associations
          const mediaResponse = await apiRequest('GET', `/api/campaigns/${savedCampaignId}/media`);
          const currentMedia = await mediaResponse.json();

          // Delete each media/slide association
          for (const item of currentMedia) {
            await apiRequest('DELETE', `/api/campaign-media/${savedCampaignId}/${item.mediaId}`);
          }
          console.log("Successfully removed all existing media/slides");
        } catch (error) {
          console.error("Error removing media/slides:", error);
          throw error;
        }

        console.log("Step 2: Removing existing screens from campaign");
        try {
          // Get current screen associations
          const screensResponse = await apiRequest('GET', `/api/campaigns/${savedCampaignId}/screens`);
          const currentScreens = await screensResponse.json();

          // Delete each screen association
          for (const screen of currentScreens) {
            await apiRequest('DELETE', `/api/campaign-screens/${savedCampaignId}/${screen.screenId}`);
          }
          console.log("Successfully removed all existing screens");
        } catch (error) {
          console.error("Error removing screens:", error);
          throw error;
        }
      }

      // 3. Add selected media and slides
      console.log("Step 3: Adding media/slides to campaign");
      try {
        for (let i = 0; i < selectedMedia.length; i++) {
          const item = selectedMedia[i];

          // Better detection for slide type
          // Check multiple possible indicators that it's a slide
          const isSlide = (
            ('type' in item && item.type === 'slide') ||
            ('content' in item) ||  // Slides have content field
            ('slideWidth' in item) ||  // Slides have slideWidth field
            (item.id && slides.some(s => s.id === item.id))  // Check if ID matches any slide
          );

          console.log(`Saving item [${i}]: ${item.name}, isSlide: ${isSlide}, id: ${item.id}`);

          await apiRequest('POST', '/api/campaign-media', {
            campaignId: savedCampaignId,
            mediaId: item.id,
            displayOrder: i,
            campaignType: isSlide ? 1 : 0
          });
        }
        console.log("Successfully added all media/slides");
      } catch (error) {
        console.error("Error adding media/slides:", error);
        throw error;
      }

      // 4. Add selected screens
      console.log("Step 4: Adding screens to campaign");
      try {
        for (const screen of selectedScreens) {
          await apiRequest('POST', '/api/campaign-screens', {
            campaignId: savedCampaignId,
            screenId: screen.id
          });
        }
        console.log("Successfully added all screens");
      } catch (error) {
        console.error("Error adding screens:", error);
        throw error;
      }

      // Invalidate all relevant queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/campaigns`] });

      if (!isNewCampaign) {
        queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${savedCampaignId}`] });
        queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${savedCampaignId}/media`] });
        queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${savedCampaignId}/screens`] });
      }

      // Invalidate all-campaign media and screens queries
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/all-campaign-media`] });
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/all-campaign-screens`] });

      toast({
        title: "Success",
        description: isNewCampaign ? "Campaign created successfully" : "Campaign updated successfully"
      });

      // Navigate back to campaigns list
      navigate('/campaigns');

    } catch (error: any) {
      console.error("Error saving campaign:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save campaign",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle media/slides selection
  const toggleMediaSelection = (item: Media | Slide, e?: React.MouseEvent) => {
    // Prevent event propagation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Determine if this is a slide
    const isSlide = 'slideConfig' in item || 'content' in item || item.type === 'slide';
    const itemWithType = isSlide ? { ...item, type: 'slide' } : item;

    // Check if item is already selected
    const exists = selectedMedia.some(m => m.id === item.id);

    // Update selection state
    if (exists) {
      // 1. Remove from selected list
      setSelectedMedia(prevMedia => prevMedia.filter(m => m.id !== item.id));

      // 2. Find original item in allMediaItems or allSlides (to preserve all properties)
      let originalItem;
      if (isSlide) {
        originalItem = allSlides.find(s => s.id === item.id);
        if (originalItem) {
          // 3. Add back to available slides list
          setSlides(prevSlides => [...prevSlides, originalItem as Slide]);
        }
      } else {
        originalItem = allMediaItems.find(m => m.id === item.id);
        if (originalItem) {
          // 3. Add back to available media list
          setMediaItems(prevMedia => [...prevMedia, originalItem as Media]);
        }
      }

      console.log(`Removed item ${item.id} (${item.name}) from selected list and added back to available list`);
    } else {
      // 1. Add to selected list
      setSelectedMedia(prevMedia => [...prevMedia, itemWithType]);

      // 2. Remove from the appropriate available list
      if (isSlide) {
        setSlides(prevSlides => prevSlides.filter(s => s.id !== item.id));
      } else {
        setMediaItems(prevMedia => prevMedia.filter(m => m.id !== item.id));
      }

      console.log(`Added item ${item.id} (${item.name}) to selected list and removed from available list`);
    }
  };

  // Handle screen selection
  const toggleScreenSelection = async (screen: Screen, e?: React.MouseEvent) => {
    // Prevent event propagation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Check if screen is already selected
    const exists = selectedScreens.some(s => s.id === screen.id);

    // Log the current state before any changes
    console.log(`TOGGLE: Screen ${screen.name} (${screen.id}) currently ${exists ? "is" : "is not"} selected`);
    if (screen.tags) {
      console.log(`TOGGLE: Screen has ${screen.tags.length} tags already attached`);
    } else {
      console.log(`TOGGLE: Screen does not have tags property`);
    }

    // Update selection state with functional updates to avoid race conditions
    if (exists) {
      // 1. Remove from selected
      setSelectedScreens(prevScreens => prevScreens.filter(s => s.id !== screen.id));

      // 2. Find original screen in allScreens (to preserve all properties)
      const originalScreen = allScreens.find(s => s.id === screen.id);
      if (originalScreen) {
        // 3. Add back to available screens list
        setScreens(prevScreens => [...prevScreens, originalScreen]);
        console.log(`Removed screen ${screen.id} (${screen.name}) from selected list and added back to available list`);
      } else {
        console.warn(`Could not find original screen with ID ${screen.id} in allScreens`);
      }
    } else {
      try {
        // First see if we have a fully populated screen in our allScreens list
        const fullScreenDetails = allScreens.find(s => s.id === screen.id);

        if (fullScreenDetails && fullScreenDetails.tags) {
          // We have a master screen with tags already loaded
          console.log(`Using master screen with ${fullScreenDetails.tags.length} tags for ${screen.name}`);

          // Add the fully populated screen to selected
          setSelectedScreens(prevScreens => [...prevScreens, fullScreenDetails]);

          // Remove from available
          setScreens(prevScreens => prevScreens.filter(s => s.id !== screen.id));

          console.log(`Added screen ${screen.name} with ${fullScreenDetails.tags.length} tags to selected list`);
        } else {
          // Need to fetch tags for this screen
          console.log(`Fetching tags for newly selected screen: ${screen.name} (${screen.id})`);
          const tagsResponse = await apiRequest('GET', `/api/screens/${screen.id}/tags`);
          const tags = await tagsResponse.json();

          // Create a screen with tags
          const screenWithTags = {
            ...screen,
            tags: tags
          };

          console.log(`✅ Fetched ${tags.length} tags for screen ${screen.name}`);

          // Add to selected with tags
          setSelectedScreens(prevScreens => [...prevScreens, screenWithTags]);

          // Remove from available
          setScreens(prevScreens => prevScreens.filter(s => s.id !== screen.id));

          console.log(`Added screen ${screen.id} (${screen.name}) to selected list with ${tags.length} tags`);
        }
      } catch (error) {
        console.error(`Failed to fetch tags for screen ${screen.id}:`, error);

        // Continue with selection but with empty tags array to avoid null/undefined issues
        const safeScreen = { ...screen, tags: [] };
        setSelectedScreens(prevScreens => [...prevScreens, safeScreen]);
        setScreens(prevScreens => prevScreens.filter(s => s.id !== screen.id));

        console.log(`Added screen ${screen.id} with empty tags array due to fetch error`);
      }
    }

    // After the state updates, log the state of all selected screens
    setTimeout(() => {
      logScreenTagsState("After toggleScreenSelection");
    }, 100);
  };

  // Move media/slide up in order
  const moveItemUp = (index: number) => {
    if (index === 0) return;
    setSelectedMedia(prevMedia => {
      const newOrder = [...prevMedia];
      [newOrder[index - 1], newOrder[index]] = [newOrder[index], newOrder[index - 1]];
      return newOrder;
    });
  };

  // Move media/slide down in order
  const moveItemDown = (index: number) => {
    if (index === selectedMedia.length - 1) return;
    setSelectedMedia(prevMedia => {
      const newOrder = [...prevMedia];
      [newOrder[index], newOrder[index + 1]] = [newOrder[index + 1], newOrder[index]];
      return newOrder;
    });
  };

  // Filter media by search and tags
  const filteredMedia = mediaItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(mediaSearchQuery.toLowerCase());

    // Handle media type filters (image, video, slide)
    if (selectedMediaTag === "image") {
      return matchesSearch && (item.fileType?.toLowerCase().includes('image') || false);
    } else if (selectedMediaTag === "video") {
      return matchesSearch && (item.fileType?.toLowerCase().includes('video') || false);
    } else if (selectedMediaTag === "slide") {
      return false; // Don't show media in slide section
    } else if (selectedMediaTag && !["image", "video", "slide"].includes(selectedMediaTag)) {
      // This must be a tag ID - check if media has this tag
      return matchesSearch && item.tags?.some((tag: any) => tag.id === selectedMediaTag);
    }

    // No filter
    return matchesSearch;
  });

  // Filter slides by search
  const filteredSlides = slides.filter(slide =>
    slide.name.toLowerCase().includes(mediaSearchQuery.toLowerCase())
  );

  // Helper function to log the state of screen tags
  const logScreenTagsState = (title: string) => {
    console.log(`===== ${title} =====`);
    selectedScreens.forEach(screen => {
      console.log(`Screen ${screen.name} (${screen.id}) has ${screen.tags ? screen.tags.length : 0} tags:`,
        screen.tags ? screen.tags.map((t: any) => t.name).join(', ') : 'none');
    });
    console.log("=================");
  };

  // Function to refresh tags for selected screens
  const refreshSelectedScreenTags = async () => {
    if (selectedScreens.length === 0) return;

    console.log("🔄 Refreshing tags for all selected screens...");

    // Use the SAME PATTERN as loadAllScreens
    const refreshedScreens = await Promise.all(selectedScreens.map(async (screen) => {
      try {
        const tagsResponse = await apiRequest('GET', `/api/screens/${screen.id}/tags`);
        const tags = await tagsResponse.json();
        if (tags && tags.length > 0) {
          console.log(`✅ Refreshed ${tags.length} tags for ${screen.name}: ${tags.map((t: any) => t.name).join(', ')}`);
        } else {
          console.log(`ℹ️ No tags found for ${screen.name} during refresh`);
        }

        // IMPORTANT: Use the same pattern as loadAllScreens
        return { ...screen, tags };
      } catch (error) {
        console.error(`Error refreshing tags for screen ${screen.id}:`, error);
        return { ...screen, tags: [] };
      }
    }));

    // Update state with new data
    setSelectedScreens(refreshedScreens);

    // Log the refreshed state
    setTimeout(() => {
      logScreenTagsState("After tag refresh");
    }, 100);
  };

  // Display tag state for debugging and trigger refresh when tag filter changes
  useEffect(() => {
    if (selectedScreens.length > 0 && selectedScreenTag) {
      logScreenTagsState("Selected screens tag state when filter changes");

      // Refresh tags to ensure filters work properly
      refreshSelectedScreenTags();
    }
  }, [selectedScreenTag]);

  // Helper function to check if a screen matches the current filters
  const screenMatchesFilters = (screen: any) => {
    // Debug log for tags in selected screens
    if (selectedScreenTag && (!screen.tags || screen.tags.length === 0)) {
      console.log(`Screen ${screen.name} (${screen.id}) has no tags while filtering for tag ${selectedScreenTag}`);
    }

    const matchesSearch =
      screen.name.toLowerCase().includes(screenSearchQuery.toLowerCase()) ||
      (screen.location && screen.location.toLowerCase().includes(screenSearchQuery.toLowerCase()));

    const matchesTag = selectedScreenTag
      ? screen.tags && screen.tags.some((tag: any) => tag.id === selectedScreenTag)
      : true;

    const matchesStatus = selectedScreenStatus
      ? screen.status === selectedScreenStatus
      : true;

    // Debug log for matching
    if (selectedScreenTag) {
      if (matchesTag) {
        console.log(`✅ Screen ${screen.name} MATCHES tag ${selectedScreenTag}`);
      } else {
        console.log(`❌ Screen ${screen.name} DOESN'T MATCH tag ${selectedScreenTag}. Available tags:`,
          screen.tags && screen.tags.length > 0
            ? screen.tags.map((t: any) => ({ id: t.id, name: t.name }))
            : 'none');
      }
    }

    return matchesSearch && matchesTag && matchesStatus;
  };

  // Filter available screens by search, tags, and status
  const filteredScreens = screens.filter(screenMatchesFilters);

  // Filter selected screens by the same criteria
  const filteredSelectedScreens = selectedScreens.filter(screenMatchesFilters);

  // Function to select all filtered screens
  const selectAllFilteredScreens = async () => {
    // Use Promise.all to handle async operations concurrently
    await Promise.all(
      filteredScreens.map(async (screen) => {
        // Only add screens that aren't already selected
        const exists = selectedScreens.some(s => s.id === screen.id);
        if (!exists) {
          await toggleScreenSelection(screen);
        }
      })
    );
    toast({
      title: "Screens added",
      description: `Added ${filteredScreens.length} screens to your selection`,
    });
  };

  // Function to remove all filtered selected screens
  const removeAllFilteredScreens = async () => {
    if (filteredSelectedScreens.length === 0) return;

    // Use Promise.all to handle async operations concurrently
    await Promise.all(
      filteredSelectedScreens.map(async (screen) => {
        await toggleScreenSelection(screen, new Event('click') as unknown as React.MouseEvent);
      })
    );
    toast({
      title: "Screens removed",
      description: `Removed ${filteredSelectedScreens.length} screens from your selection`,
    });
  };

  if (loadingData) {
    return (
      <MainLayout>
        <div className="container">
          <div className="flex items-center mb-6">
            <Button
              variant="ghost"
              className="mr-2"
              onClick={() => navigate('/campaigns')}
            >
              <ChevronLeft size={16} className="mr-1" />
              Back to Campaigns
            </Button>
            <h1 className="text-2xl font-bold">
              {isNewCampaign ? "New Campaign" : "Edit Campaign"}
            </h1>
          </div>
          <div className="flex items-center justify-center h-[60vh]">
            <p>Loading campaign data...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            className="mr-2"
            onClick={() => navigate('/campaigns')}
          >
            <ChevronLeft size={16} className="mr-1" />
            Back to Campaigns
          </Button>
          <h1 className="text-2xl font-bold">
            {isNewCampaign ? "New Campaign" : "Edit Campaign"}
          </h1>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSaveCampaign)} className="space-y-6">
            <Tabs
              defaultValue="details"
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details" className="flex items-center">
                  <ListChecks className="w-4 h-4 mr-2" />
                  Details
                </TabsTrigger>
                <TabsTrigger value="content" className="flex items-center">
                  <ImageIcon className="w-4 h-4 mr-2" />
                  Content
                </TabsTrigger>
                <TabsTrigger value="screens" className="flex items-center">
                  <Monitor className="w-4 h-4 mr-2" />
                  Screens
                </TabsTrigger>
              </TabsList>

              {/* Details Tab */}
              <TabsContent value="details" className="pt-4">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Campaign Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter campaign name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="startDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Start Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>End Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="scheduled">Scheduled</SelectItem>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="paused">Paused</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter campaign description"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Trigger Type Section */}
                  <div className="space-y-4 pt-4 border-t">
                    <FormField
                      control={form.control}
                      name="triggerType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            Trigger Type
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                                  <Info className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-80">
                                <div className="space-y-2">
                                  <h4 className="font-medium">Trigger Types</h4>
                                  <div className="space-y-2 text-sm">
                                    <div><strong>Default Loop:</strong> Campaign will be displayed in a loop</div>
                                    <div><strong>Geofencing:</strong> Campaign will be displayed when the screen location is within the specified radius of the latitude and longitude. It will display in the order it is added to the screen campaigns list.</div>
                                    <div><strong>Weather:</strong> Campaign will be displayed when the screen location's temperature is within the specified range. It will display in the order it is added to the screen campaigns list.</div>
                                    <div><strong>Audience Proximity:</strong> Campaign will be displayed when the user is within the specified distance from the screen. It will override the Default Loop, Geofencing and Weather trigger types.</div>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          </FormLabel>
                          <Select value={field.value?.toString()} onValueChange={(value) => field.onChange(parseInt(value))}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select trigger type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="1">Default Loop</SelectItem>
                              <SelectItem value="2">Geofencing</SelectItem>
                              <SelectItem value="3">Weather</SelectItem>
                              <SelectItem value="4">Audience Proximity</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Geofencing Fields */}
                    {form.watch('triggerType') === 2 && (
                      <div className="space-y-4 pl-4 border-l-2 border-muted">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="latitude"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Latitude</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="any"
                                    placeholder="e.g., 40.7128"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                    value={field.value || ''}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="longitude"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Longitude</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="any"
                                    placeholder="e.g., -74.0060"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                    value={field.value || ''}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="radius"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Radius (meters)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="any"
                                    placeholder="e.g., 100"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                    value={field.value || ''}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    )}

                    {/* Weather Fields */}
                    {form.watch('triggerType') === 3 && (
                      <div className="space-y-4 pl-4 border-l-2 border-muted">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="minTemp"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Min Temperature</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="any"
                                    placeholder="e.g., 20"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                    value={field.value || ''}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="maxTemp"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Max Temperature</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="any"
                                    placeholder="e.g., 30"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                    value={field.value || ''}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="units"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="flex items-center gap-2">
                                  Units
                                  <Popover>
                                    <PopoverTrigger asChild>
                                      <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                                        <Info className="h-4 w-4 text-muted-foreground" />
                                      </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-80">
                                      <div className="space-y-2">
                                        <h4 className="font-medium">Temperature Units</h4>
                                        <div className="space-y-2 text-sm">
                                          <div><strong>Standard Units:</strong> Fahrenheit for temperature and Inches for precipitation</div>
                                          <div><strong>Metric Units:</strong> Celsius for temperature and Millimeters for precipitation</div>
                                          <div><strong>Imperial Units:</strong> Fahrenheit for temperature and Inches for precipitation</div>
                                        </div>
                                      </div>
                                    </PopoverContent>
                                  </Popover>
                                </FormLabel>
                                <Select value={field.value} onValueChange={field.onChange}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select units" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="standard">Standard</SelectItem>
                                    <SelectItem value="metric">Metric</SelectItem>
                                    <SelectItem value="imperial">Imperial</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    )}

                    {/* Audience Proximity Fields */}
                    {form.watch('triggerType') === 4 && (
                      <div className="space-y-4 pl-4 border-l-2 border-muted">
                        <FormField
                          control={form.control}
                          name="distance"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center gap-2">
                                Distance (meters)
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                                      <Info className="h-4 w-4 text-muted-foreground" />
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-80">
                                    <div className="space-y-2">
                                      <h4 className="font-medium">Distance Recommendations</h4>
                                      <div className="space-y-2 text-sm">
                                        <div><strong>Recommended value:</strong> 2 meters</div>
                                        <div><strong>Min value:</strong> 0.5 meters</div>
                                        <div><strong>Max value:</strong> 10 meters</div>
                                      </div>
                                    </div>
                                  </PopoverContent>
                                </Popover>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  step="any"
                                  placeholder="e.g., 2"
                                  {...field}
                                  onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                  value={field.value || ''}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-between mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/campaigns')}
                  >
                    Cancel
                  </Button>
                  <Button type="button" onClick={() => setActiveTab('content')}>
                    Next: Content
                  </Button>
                </div>
              </TabsContent>

              {/* Content Tab */}
              <TabsContent value="content" className="pt-4">
                {/* Search Filter Section */}
                <div className="px-3 py-4 border-b mb-4">
                  {/* Search and Type in a horizontal layout */}
                  <div className="flex flex-col md:flex-row gap-4 mb-6">
                    {/* Search input */}
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search media and slides..."
                        value={mediaSearchQuery}
                        onChange={(e) => setMediaSearchQuery(e.target.value)}
                        className="pl-9 h-10"
                      />
                    </div>

                    {/* Type filter */}
                    <div className="flex items-center gap-3">
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">TYPE</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant={selectedMediaTag === "" ? "default" : "outline"}
                          size="sm"
                          type="button"
                          onClick={() => setSelectedMediaTag("")}
                          className="h-8 text-xs rounded-full px-4 bg-primary text-white border-none"
                        >
                          All Types
                        </Button>
                        <Button
                          variant={selectedMediaTag === "image" ? "default" : "outline"}
                          size="sm"
                          type="button"
                          onClick={() => setSelectedMediaTag("image")}
                          className={`h-8 text-xs rounded-full px-4 flex items-center gap-1.5 ${selectedMediaTag === "image" ? "bg-primary text-white border-none" : "bg-white"}`}
                        >
                          <ImageIcon className="h-3 w-3" /> Images
                        </Button>
                        <Button
                          variant={selectedMediaTag === "video" ? "default" : "outline"}
                          size="sm"
                          type="button"
                          onClick={() => setSelectedMediaTag("video")}
                          className={`h-8 text-xs rounded-full px-4 flex items-center gap-1.5 ${selectedMediaTag === "video" ? "bg-primary text-white border-none" : "bg-white"}`}
                        >
                          <FileVideo className="h-3 w-3" /> Videos
                        </Button>
                        <Button
                          variant={selectedMediaTag === "slide" ? "default" : "outline"}
                          size="sm"
                          type="button"
                          onClick={() => setSelectedMediaTag("slide")}
                          className={`h-8 text-xs rounded-full px-4 flex items-center gap-1.5 ${selectedMediaTag === "slide" ? "bg-primary text-white border-none" : "bg-white"}`}
                        >
                          <Presentation className="h-3 w-3" /> Slides
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Tags filter - horizontal with label */}
                  {mediaTags && mediaTags.length > 0 && (
                    <div className="flex items-start">
                      <div className="w-20">
                        <span className="text-sm font-medium text-muted-foreground">TAGS</span>
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-wrap gap-2">
                          {mediaTags.map(tag => (
                            <Button
                              key={tag.id}
                              variant={selectedMediaTag === tag.id ? "default" : "outline"}
                              size="sm"
                              type="button"
                              onClick={() => setSelectedMediaTag(tag.id)}
                              className="h-8 text-xs rounded-full px-4"
                              style={tag.color ? {
                                borderColor: tag.color,
                                color: selectedMediaTag === tag.id ? 'white' : tag.color,
                                backgroundColor: selectedMediaTag === tag.id ? tag.color : 'transparent'
                              } : {}}
                            >
                              {tag.name}
                            </Button>
                          ))}
                          {/* Clear tags button */}
                          {selectedMediaTag && !['image', 'video', 'slide', ''].includes(selectedMediaTag) && (
                            <Button
                              variant="secondary"
                              size="sm"
                              type="button"
                              onClick={() => setSelectedMediaTag("")}
                              className="h-8 text-xs rounded-full px-4 flex items-center gap-1.5"
                            >
                              <X className="h-3 w-3" /> Clear Filter
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Available Content Section */}
                  <div className="bg-background rounded-lg border shadow-sm">
                    <div className="p-6 border-b">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-lg font-medium flex items-center">
                            <span className="bg-primary/10 rounded-full p-1.5 mr-2">
                              <ImageIcon className="h-4 w-4 text-primary" />
                            </span>
                            Available Content
                          </h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            Select and arrange media files and slides for this campaign
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Tabs for Media Files and Slides */}
                    <div className="p-6">
                      <Tabs defaultValue="media_files" className="w-full">
                        <TabsList className="grid w-full grid-cols-2 mb-4">
                          <TabsTrigger
                            value="media_files"
                            className="flex items-center gap-1.5"
                            onClick={() => setSelectedMediaTag(selectedMediaTag === "slide" ? "" : selectedMediaTag)}
                          >
                            <FileImage className="h-4 w-4" />
                            Media Files
                          </TabsTrigger>
                          <TabsTrigger
                            value="slides"
                            className="flex items-center gap-1.5"
                            onClick={() => setSelectedMediaTag("slide")}
                          >
                            <Presentation className="h-4 w-4" />
                            Slides
                          </TabsTrigger>
                        </TabsList>

                        {/* Media Files Tab Content */}
                        <TabsContent value="media_files" className="mt-0">
                          {filteredMedia.length === 0 ? (
                            <div className="py-8 text-center border rounded-md">
                              <FolderSearch className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                              <p className="text-sm text-muted-foreground">No media files match your filters</p>
                            </div>
                          ) : (
                            <div className="border rounded-md divide-y max-h-[300px] overflow-y-auto">
                              {filteredMedia.map((media) => (
                                <div
                                  key={media.id}
                                  className="p-3 flex items-center cursor-pointer hover:bg-muted/30 transition-colors"
                                  onClick={() => toggleMediaSelection(media)}
                                >
                                  <div className="flex-shrink-0 w-6 h-6 rounded-full border mr-2 flex items-center justify-center bg-primary/10">
                                    <Plus className="h-3 w-3 text-primary" />
                                  </div>
                                  <div className="flex-shrink-0 h-12 w-12 rounded-md overflow-hidden mr-3 bg-muted flex items-center justify-center border">
                                    {/* Show thumbnails for both images and videos */}
                                    {media.fileType?.startsWith("image/") || media.thumbnail_url || media.thumbnailUrl ? (
                                      <img
                                        src={media.thumbnail_url || media.thumbnailUrl || media.fileUrl}
                                        alt={media.name}
                                        className="h-full w-full object-cover"
                                      />
                                    ) : media.fileType?.startsWith("video/") ? (
                                      /* If there's no thumbnail, show video icon with background color */
                                      <div className="bg-primary/10 h-full w-full flex items-center justify-center">
                                        <FileVideo className="h-5 w-5 text-muted-foreground" />
                                      </div>
                                    ) : (
                                      <FileQuestion className="h-5 w-5 text-muted-foreground" />
                                    )}
                                  </div>
                                  <div className="flex-1 overflow-hidden">
                                    <p className="truncate text-sm font-medium">{media.name}</p>
                                    <div className="text-xs text-muted-foreground truncate flex items-center mt-0.5">
                                      <Badge className="rounded-full py-0 px-2 h-4 font-normal text-[10px] bg-muted text-muted-foreground">
                                        {media.fileType?.split("/")[0] || "unknown"}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </TabsContent>

                        {/* Slides Tab Content */}
                        <TabsContent value="slides" className="mt-0">
                          {filteredSlides.length === 0 ? (
                            <div className="py-8 text-center border rounded-md">
                              <FolderSearch className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                              <p className="text-sm text-muted-foreground">No slides match your filters</p>
                            </div>
                          ) : (
                            <div className="border rounded-md divide-y max-h-[300px] overflow-y-auto">
                              {filteredSlides.map((slide) => (
                                <div
                                  key={slide.id}
                                  className="p-3 flex items-center cursor-pointer hover:bg-muted/30 transition-colors"
                                  onClick={() => toggleMediaSelection(slide)}
                                >
                                  <div className="flex-shrink-0 w-6 h-6 rounded-full border mr-2 flex items-center justify-center bg-primary/10">
                                    <Plus className="h-3 w-3 text-primary" />
                                  </div>
                                  <div className="flex-shrink-0 h-12 w-12 rounded-md overflow-hidden mr-3 bg-primary/5 flex items-center justify-center border">
                                    {slide.thumbnailUrl ? (
                                      <img
                                        src={slide.thumbnailUrl}
                                        alt={slide.name}
                                        className="h-full w-full object-cover"
                                      />
                                    ) : (
                                      <Presentation className="h-5 w-5 text-primary" />
                                    )}
                                  </div>
                                  <div className="flex-1 overflow-hidden">
                                    <p className="truncate text-sm font-medium">{slide.name}</p>
                                    <div className="text-xs text-muted-foreground truncate flex items-center mt-0.5">
                                      <Badge className="rounded-full py-0 px-2 h-4 font-normal text-[10px] bg-primary/10 text-primary">
                                        Slide
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>

                  {/* Selected Content Section */}
                  <div className="bg-background rounded-lg border shadow-sm">
                    <div className="p-6 border-b">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-lg font-medium flex items-center">
                            <span className="bg-primary/10 rounded-full p-1.5 mr-2">
                              <ListOrdered className="h-4 w-4 text-primary" />
                            </span>
                            Selected Content
                          </h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            Arrange the order in which content will be displayed
                          </p>
                        </div>
                        <Badge variant="outline" className="gap-1.5">
                          <Layers className="h-3.5 w-3.5" /> {selectedMedia.length} Items
                        </Badge>
                      </div>
                    </div>

                    {selectedMedia.length === 0 ? (
                      <div className="p-12 text-center">
                        <div className="w-16 h-16 rounded-full bg-muted/40 flex items-center justify-center mx-auto mb-4">
                          <FileQuestion className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium">No content selected</h3>
                        <p className="text-sm text-muted-foreground mt-1 max-w-md mx-auto mb-6">
                          Select media files and slides from the available content section
                        </p>
                      </div>
                    ) : (
                      <div className="divide-y max-h-[500px] overflow-y-auto">
                        {selectedMedia.map((item, index) => {
                          // Check if it's a slide
                          const isSlide = (
                            ('type' in item && item.type === 'slide') ||
                            ('content' in item) ||
                            ('slideWidth' in item) ||
                            (item.id && slides.some(s => s.id === item.id))
                          );

                          return (
                            <div key={item.id} className="flex items-center p-4 hover:bg-muted/30 transition-colors group">
                              <div className="mr-3 font-mono text-xs bg-primary/10 text-primary h-6 w-6 rounded-full flex items-center justify-center font-medium">
                                {index + 1}
                              </div>
                              {isSlide ? (
                                <div className="mr-3 h-14 w-14 overflow-hidden rounded-md border shadow-sm flex-shrink-0 bg-primary/5 flex items-center justify-center">
                                  {item.thumbnailUrl ? (
                                    <img
                                      src={item.thumbnailUrl}
                                      alt={item.name}
                                      className="h-full w-full object-cover"
                                    />
                                  ) : (
                                    <Presentation className="h-6 w-6 text-primary" />
                                  )}
                                </div>
                              ) : (
                                <div className="mr-3 h-14 w-14 overflow-hidden rounded-md border shadow-sm flex-shrink-0 bg-muted flex items-center justify-center">
                                  {/* Show thumbnails for both images and videos */}
                                  {item.fileType?.startsWith("image/") || item.thumbnail_url || item.thumbnailUrl ? (
                                    <img
                                      src={item.thumbnail_url || item.thumbnailUrl || item.fileUrl}
                                      alt={item.name}
                                      className="h-full w-full object-cover"
                                    />
                                  ) : item.fileType?.startsWith("video/") ? (
                                    /* If there's no thumbnail, show video icon with background color */
                                    <div className="bg-primary/10 h-full w-full flex items-center justify-center">
                                      <FileVideo className="h-6 w-6 text-muted-foreground" />
                                    </div>
                                  ) : (
                                    <FileQuestion className="h-6 w-6 text-muted-foreground" />
                                  )}
                                </div>
                              )}
                              <div className="flex-1 min-w-0 ml-1">
                                <p className="text-sm font-medium truncate">{item.name}</p>
                                <div className="flex items-center mt-1">
                                  <Badge className={cn(
                                    "rounded-full text-xs font-normal py-0 px-2",
                                    isSlide
                                      ? "bg-primary/10 text-primary"
                                      : "bg-secondary/80 text-secondary-foreground"
                                  )}>
                                    {isSlide ? "Slide" : (item.fileType?.split("/")[0] || "File")}
                                  </Badge>
                                </div>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-muted-foreground hover:text-foreground rounded-full opacity-80 group-hover:opacity-100 transition-opacity"
                                  title="Move up"
                                  onClick={() => moveItemUp(index)}
                                  disabled={index === 0}
                                >
                                  <ArrowUp className="h-4 w-4" />
                                </Button>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-muted-foreground hover:text-foreground rounded-full opacity-80 group-hover:opacity-100 transition-opacity"
                                  title="Move down"
                                  onClick={() => moveItemDown(index)}
                                  disabled={index === selectedMedia.length - 1}
                                >
                                  <ArrowDown className="h-4 w-4" />
                                </Button>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-muted-foreground hover:text-destructive rounded-full opacity-80 group-hover:opacity-100 transition-opacity"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedMedia(prevMedia => prevMedia.filter(m => m.id !== item.id));
                                  }}
                                  title="Remove"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-between mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setActiveTab('details')}
                  >
                    Back: Details
                  </Button>
                  <Button type="button" onClick={() => setActiveTab('screens')}>
                    Next: Screens
                  </Button>
                </div>
              </TabsContent>

              {/* Screens Tab */}
              <TabsContent value="screens" className="pt-4">
                {/* Search Filter Section */}
                <div className="px-3 py-4 border-b mb-4">
                  {/* Search and Status in a horizontal layout */}
                  <div className="flex flex-col md:flex-row gap-4 mb-6">
                    {/* Search input */}
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search screens by name or location..."
                        value={screenSearchQuery}
                        onChange={(e) => setScreenSearchQuery(e.target.value)}
                        className="pl-9 h-10"
                      />
                    </div>

                    {/* Status filter */}
                    <div className="flex items-center gap-3">
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">STATUS</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant={!selectedScreenStatus ? "default" : "outline"}
                          size="sm"
                          type="button"
                          onClick={() => setSelectedScreenStatus("")}
                          className="h-8 text-xs rounded-full px-4 bg-primary text-white border-none"
                        >
                          All Statuses
                        </Button>
                        <Button
                          variant={selectedScreenStatus === "online" ? "default" : "outline"}
                          size="sm"
                          type="button"
                          onClick={() => setSelectedScreenStatus("online")}
                          className={`h-8 text-xs rounded-full px-4 flex items-center gap-1.5 ${selectedScreenStatus === "online" ? "bg-primary text-white border-none" : "bg-white"}`}
                        >
                          <div className="w-2 h-2 rounded-full bg-green-500"></div> Online
                        </Button>
                        <Button
                          variant={selectedScreenStatus === "offline" ? "default" : "outline"}
                          size="sm"
                          type="button"
                          onClick={() => setSelectedScreenStatus("offline")}
                          className={`h-8 text-xs rounded-full px-4 flex items-center gap-1.5 ${selectedScreenStatus === "offline" ? "bg-primary text-white border-none" : "bg-white"}`}
                        >
                          <div className="w-2 h-2 rounded-full bg-gray-400"></div> Offline
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Tags filter - horizontal with label */}
                  {screenTags && screenTags.length > 0 && (
                    <div className="flex items-start">
                      <div className="w-20">
                        <span className="text-sm font-medium text-muted-foreground">TAGS</span>
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-wrap gap-2">
                          {screenTags.map(tag => (
                            <Button
                              key={tag.id}
                              variant={selectedScreenTag === tag.id ? "default" : "outline"}
                              size="sm"
                              type="button"
                              onClick={() => setSelectedScreenTag(selectedScreenTag === tag.id ? "" : tag.id)}
                              className="h-8 text-xs rounded-full px-4"
                              style={tag.color ? {
                                borderColor: tag.color,
                                color: selectedScreenTag === tag.id ? 'white' : tag.color,
                                backgroundColor: selectedScreenTag === tag.id ? tag.color : 'transparent'
                              } : {}}
                            >
                              {tag.name}
                            </Button>
                          ))}
                          {/* Clear tags button */}
                          {selectedScreenTag && (
                            <Button
                              variant="secondary"
                              size="sm"
                              type="button"
                              onClick={() => setSelectedScreenTag("")}
                              className="h-8 text-xs rounded-full px-4 flex items-center gap-1.5"
                            >
                              <X className="h-3 w-3" /> Clear Filter
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Available Screens Section */}
                  <div className="bg-background rounded-lg border shadow-sm">
                    <div className="p-6 border-b">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-lg font-medium flex items-center">
                            <span className="bg-primary/10 rounded-full p-1.5 mr-2">
                              <Monitor className="h-4 w-4 text-primary" />
                            </span>
                            Available Screens
                          </h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            Select screens to display this campaign's content
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={selectAllFilteredScreens}
                          disabled={filteredScreens.length === 0}
                          className="flex items-center gap-1.5"
                        >
                          <Check className="h-3.5 w-3.5" />
                          Select All
                          {filteredScreens.length > 0 && (
                            <Badge variant="secondary" className="ml-1 text-xs">
                              {filteredScreens.length}
                            </Badge>
                          )}
                        </Button>
                      </div>
                    </div>
                    <div className="p-6">
                      {filteredScreens.length === 0 ? (
                        <div className="py-8 text-center border rounded-md">
                          <MonitorX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                          <p className="text-sm text-muted-foreground">No screens match your filters</p>
                        </div>
                      ) : (
                        <div className="border rounded-md divide-y max-h-[400px] overflow-y-auto">
                          {filteredScreens.map((screen) => (
                            <div
                              key={screen.id}
                              className="p-3 flex items-center cursor-pointer hover:bg-muted/30 transition-colors"
                              onClick={() => toggleScreenSelection(screen)}
                            >
                              <div className="flex-shrink-0 w-6 h-6 rounded-full border mr-2 flex items-center justify-center bg-primary/10">
                                <Plus className="h-3 w-3 text-primary" />
                              </div>
                              <div className={cn(
                                "flex-shrink-0 h-12 w-12 rounded-md overflow-hidden mr-3 flex items-center justify-center border",
                                screen.status === "online" ? "bg-green-500/10 border-green-500/30" : "bg-muted border-muted-foreground/20"
                              )}>
                                <Monitor className={cn(
                                  "h-5 w-5",
                                  screen.status === "online" ? "text-green-500" : "text-muted-foreground/60"
                                )} />
                              </div>
                              <div className="flex-1 overflow-hidden">
                                <p className="truncate text-sm font-medium">{screen.name}</p>
                                <p className="text-xs text-muted-foreground truncate">
                                  {screen.location || "No location"}
                                </p>
                                <div className="flex items-center mt-1">
                                  <Badge className={cn(
                                    "rounded-full py-0 h-5 px-2 text-xs font-normal",
                                    screen.status === "online"
                                      ? "bg-green-500/15 text-green-600 border-green-600/20"
                                      : "bg-gray-200 text-gray-500 border-gray-300"
                                  )}>
                                    {screen.status}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Selected Screens Section */}
                  <div className="bg-background rounded-lg border shadow-sm">
                    <div className="p-6 border-b">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-lg font-medium flex items-center">
                            <span className="bg-primary/10 rounded-full p-1.5 mr-2">
                              <CheckCircle2 className="h-4 w-4 text-primary" />
                            </span>
                            Selected Screens
                          </h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            Screens that will display this campaign's content
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <Badge variant="outline" className="gap-1.5">
                            <MonitorSmartphone className="h-3.5 w-3.5" /> {selectedScreens.length} Screens
                          </Badge>
                          {filteredSelectedScreens.length > 0 && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={removeAllFilteredScreens}
                              className="flex items-center gap-1.5 text-destructive hover:text-destructive border-destructive/30 hover:border-destructive/50 hover:bg-destructive/10"
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                              Remove All
                              <Badge variant="secondary" className="ml-1 text-xs">
                                {filteredSelectedScreens.length}
                              </Badge>
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>

                    {selectedScreens.length === 0 ? (
                      <div className="p-12 text-center">
                        <div className="w-16 h-16 rounded-full bg-muted/40 flex items-center justify-center mx-auto mb-4">
                          <MonitorOff className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium">No screens selected</h3>
                        <p className="text-sm text-muted-foreground mt-1 max-w-md mx-auto mb-6">
                          Select screens from the available screens section above
                        </p>
                      </div>
                    ) : (
                      <div className="divide-y max-h-[350px] overflow-y-auto">
                        {filteredSelectedScreens.length === 0 && selectedScreens.length > 0 ? (
                          <div className="py-6 text-center">
                            <FileQuestion className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                            <p className="text-sm text-muted-foreground">No selected screens match your filters</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {selectedScreens.length} screen(s) are hidden by current filters
                            </p>
                          </div>
                        ) : (
                          filteredSelectedScreens.map((screen) => (
                            <div key={screen.id} className="flex items-center p-4 hover:bg-muted/30 transition-colors group">
                              <div className={cn(
                                "flex-shrink-0 h-12 w-12 rounded-md overflow-hidden mr-3 flex items-center justify-center border",
                                screen.status === "online" ? "bg-green-500/10 border-green-500/30" : "bg-muted border-muted-foreground/20"
                              )}>
                                <Monitor className={cn(
                                  "h-5 w-5",
                                  screen.status === "online" ? "text-green-500" : "text-muted-foreground/60"
                                )} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{screen.name}</p>
                                <p className="text-xs text-muted-foreground truncate">
                                  {screen.location || "No location"}
                                </p>
                                <div className="flex items-center mt-1">
                                  <Badge className={cn(
                                    "rounded-full py-0 h-5 px-2 text-xs font-normal",
                                    screen.status === "online"
                                      ? "bg-green-500/15 text-green-600 border-green-600/20"
                                      : "bg-gray-200 text-gray-500 border-gray-300"
                                  )}>
                                    {screen.status}
                                  </Badge>
                                </div>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-muted-foreground hover:text-destructive rounded-full opacity-80 group-hover:opacity-100 transition-opacity"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedScreens(prevScreens => prevScreens.filter(s => s.id !== screen.id));
                                }}
                                title="Remove"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ))
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-between mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setActiveTab('content')}
                  >
                    Back: Content
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      isNewCampaign ? 'Create Campaign' : 'Update Campaign'
                    )}
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </form>
        </Form>
      </div>
    </MainLayout>
  );
}