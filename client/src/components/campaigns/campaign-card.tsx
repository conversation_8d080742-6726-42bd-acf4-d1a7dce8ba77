import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { formatDate, calculateProgress } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Monitor, Image, MoreVertical, Edit, Trash2, Play, Pause, Copy, Presentation, MapPin, Thermometer, Users, RotateCcw } from "lucide-react";
import { useLocation } from "wouter";

import type { Campaign } from "@shared/schema";

interface CampaignCardProps {
  campaign: Campaign;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onDuplicate: (id: string) => void;
  onToggleActive: (id: string, currentStatus: string) => void;
  mediaCount?: number;
  screenCount?: number;
  slideCount?: number;
}

export function CampaignCard({
  campaign,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleActive,
  mediaCount = 0,
  screenCount = 0,
  slideCount = 0
}: CampaignCardProps) {
  const { id, name, startDate, endDate, status, triggerType } = campaign;
  const [_, navigate] = useLocation();

  const progress = calculateProgress(startDate, endDate);

  const getStatusBadgeVariant = () => {
    switch (status) {
      case "active":
        return "default" as const;
      case "scheduled":
        return "secondary" as const;
      case "paused":
        return "destructive" as const;
      case "completed":
        return "destructive" as const;
      default:
        return "outline" as const;
    }
  };

  const getTriggerTypeInfo = () => {
    switch (triggerType) {
      case 1:
        return { label: "Default Loop", icon: RotateCcw, color: "bg-blue-100 text-blue-800 border-blue-200" };
      case 2:
        return { label: "Geofencing", icon: MapPin, color: "bg-green-100 text-green-800 border-green-200" };
      case 3:
        return { label: "Weather", icon: Thermometer, color: "bg-orange-100 text-orange-800 border-orange-200" };
      case 4:
        return { label: "Audience Proximity", icon: Users, color: "bg-purple-100 text-purple-800 border-purple-200" };
      default:
        return { label: "Default Loop", icon: RotateCcw, color: "bg-blue-100 text-blue-800 border-blue-200" };
    }
  };

  const triggerInfo = getTriggerTypeInfo();

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-medium">{name}</h3>
              {status && (
                <Badge
                  variant={getStatusBadgeVariant()}
                  className={status === "active" ? "bg-[#05DAC3] text-[#020627] hover:bg-[#05DAC3]/80" : ""}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {startDate ? formatDate(startDate) : 'Not set'} - {endDate ? formatDate(endDate) : 'Not set'}
            </p>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Campaign Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => navigate(`/campaigns/${id}`)}>
                <Edit className="mr-2 h-4 w-4" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDuplicate(id)}>
                <Copy className="mr-2 h-4 w-4" /> Duplicate
              </DropdownMenuItem>
              {status && (
                status === "active" ? (
                  <DropdownMenuItem onClick={() => onToggleActive(id, status || '')}>
                    <Pause className="mr-2 h-4 w-4" /> Pause
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem onClick={() => onToggleActive(id, status || '')}>
                    <Play className="mr-2 h-4 w-4" /> Activate
                  </DropdownMenuItem>
                )
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onDelete(id)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="mt-6 mb-2">
          <div className="flex justify-between text-sm mb-1">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{progress}%</span>
          </div>
          <Progress
            value={progress}
            className={`h-2 ${
              status === "completed"
                ? "[&>div]:bg-destructive"
                : status === "active"
                  ? "[&>div]:bg-[#05DAC3]"
                  : ""
            }`}
          />
        </div>

        <div className="flex items-center mt-3 text-sm text-muted-foreground">
          <span className="flex items-center mr-3">
            <Monitor className="mr-1 h-4 w-4" /> {screenCount} screens
          </span>
          <span className="flex items-center mr-3">
            <Image className="mr-1 h-4 w-4" /> {mediaCount} media files
          </span>
          <span className="flex items-center">
            <Presentation className="mr-1 h-4 w-4" /> {slideCount} slides
          </span>
        </div>
      </CardContent>

      <CardFooter className="pt-0 pb-4 px-6">
        <Button variant="outline" className="w-full" onClick={() => navigate(`/campaigns/${id}`)}>
          <Edit className="mr-2 h-4 w-4" /> Edit Campaign
        </Button>
      </CardFooter>
    </Card>
  );
}
