import React, { useEffect } from "react";
import { Switch, Route, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { MainLayout } from "@/components/layout/main-layout";

// Pages
import Login from "@/pages/login";
import Signup from "@/pages/signup";
import ForgotPassword from "@/pages/forgot-password";
import ResetPassword from "@/pages/reset-password";
import Dashboard from "@/pages/dashboard";
import Media from "@/pages/media";
import Screens from "@/pages/screens";
import Campaigns from "@/pages/campaigns";
import CampaignDetails from "@/pages/campaign-details";
import Designer from "@/pages/designer";
import Reports from "@/pages/reports";
import Settings from "@/pages/settings";
import NotFound from "@/pages/not-found";

const AuthenticatedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth();
  const [_, navigate] = useLocation();

  // Using useLayoutEffect instead of useEffect to avoid state updates during render
  React.useLayoutEffect(() => {
    if (!loading && !user) {
      console.log('🔄 No user found, redirecting to login...');
      // Use absolute path to ensure we go to /login, not relative to current route
      navigate('/login');
    }
  }, [user, loading, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return user ? <>{children}</> : null;
};

function App() {
  return (
    <Switch>
      {/* Public routes */}
      <Route path="/login" component={Login} />
      <Route path="/signup" component={Signup} />
      <Route path="/forgot-password" component={ForgotPassword} />
      <Route path="/reset-password" component={ResetPassword} />

      {/* Protected routes */}
      <Route path="/">
        {() => <AuthenticatedRoute><Dashboard /></AuthenticatedRoute>}
      </Route>
      <Route path="/dashboard">
        {() => <AuthenticatedRoute><Dashboard /></AuthenticatedRoute>}
      </Route>
      <Route path="/media">
        {() => <AuthenticatedRoute><Media /></AuthenticatedRoute>}
      </Route>
      <Route path="/screens">
        {() => <AuthenticatedRoute><Screens /></AuthenticatedRoute>}
      </Route>
      <Route path="/campaigns">
        {() => <AuthenticatedRoute><Campaigns /></AuthenticatedRoute>}
      </Route>
      <Route path="/campaigns/:id">
        {(params) => <AuthenticatedRoute><CampaignDetails /></AuthenticatedRoute>}
      </Route>
      <Route path="/designer">
        {() => <AuthenticatedRoute><Designer /></AuthenticatedRoute>}
      </Route>
      <Route path="/reports">
        {() => <AuthenticatedRoute><Reports /></AuthenticatedRoute>}
      </Route>
      <Route path="/settings">
        {() => <AuthenticatedRoute><Settings /></AuthenticatedRoute>}
      </Route>
      <Route path="/settings/tags">
        {() => <AuthenticatedRoute><Settings /></AuthenticatedRoute>}
      </Route>

      {/* Fallback to 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

export default App;