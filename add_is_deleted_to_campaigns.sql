-- Add is_deleted column to campaigns table
-- Run this script in your Supabase SQL editor

-- Add is_deleted column with default value false
ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

-- Add comment for documentation
COMMENT ON COLUMN public.campaigns.is_deleted IS 'Soft delete flag - when true, campaign is considered deleted';

-- Verify the column was added
SELECT column_name, data_type, column_default, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'campaigns' 
AND column_name = 'is_deleted';
