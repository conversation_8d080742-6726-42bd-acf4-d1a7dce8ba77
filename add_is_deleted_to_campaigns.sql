-- Add missing columns to campaigns table
-- Run this script in your Supabase SQL editor

-- Add trigger type columns
ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS trigger_type INTEGER NOT NULL DEFAULT 1;

ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS latitude NUMERIC;

ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS longitude NUMERIC;

ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS radius NUMERIC;

ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS min_temp NUMERIC;

ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS max_temp NUMERIC;

ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS units TEXT;

ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS distance NUMERIC;

-- Add is_deleted column with default value false
ALTER TABLE public.campaigns
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

-- Add comments for documentation
COMMENT ON COLUMN public.campaigns.trigger_type IS 'Campaign trigger type: 1=Default Loop, 2=Geofencing, 3=Weather, 4=Audience Proximity';
COMMENT ON COLUMN public.campaigns.latitude IS 'Latitude for geofencing trigger (decimal degrees)';
COMMENT ON COLUMN public.campaigns.longitude IS 'Longitude for geofencing trigger (decimal degrees)';
COMMENT ON COLUMN public.campaigns.radius IS 'Radius for geofencing trigger (meters)';
COMMENT ON COLUMN public.campaigns.min_temp IS 'Minimum temperature for weather trigger';
COMMENT ON COLUMN public.campaigns.max_temp IS 'Maximum temperature for weather trigger';
COMMENT ON COLUMN public.campaigns.units IS 'Temperature units: standard, metric, or imperial';
COMMENT ON COLUMN public.campaigns.distance IS 'Distance for audience proximity trigger (meters)';
COMMENT ON COLUMN public.campaigns.is_deleted IS 'Soft delete flag - when true, campaign is considered deleted';

-- Verify all columns were added
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns
WHERE table_name = 'campaigns'
AND column_name IN ('trigger_type', 'latitude', 'longitude', 'radius', 'min_temp', 'max_temp', 'units', 'distance', 'is_deleted')
ORDER BY column_name;
