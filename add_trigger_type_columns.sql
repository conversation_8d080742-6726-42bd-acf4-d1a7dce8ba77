-- Add trigger type columns to campaigns table
-- Run this script in your Supabase SQL editor if the columns don't exist

-- Add trigger_type column (defaults to 1 for Default Loop)
ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS trigger_type INTEGER NOT NULL DEFAULT 1;

-- Add geofencing columns
ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS latitude NUMERIC;

ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS longitude NUMERIC;

ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS radius NUMERIC;

-- Add weather columns
ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS min_temp NUMERIC;

ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS max_temp NUMERIC;

ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS units TEXT;

-- Add audience proximity column
ALTER TABLE public.campaigns 
ADD COLUMN IF NOT EXISTS distance NUMERIC;

-- Add comments for documentation
COMMENT ON COLUMN public.campaigns.trigger_type IS 'Campaign trigger type: 1=Default Loop, 2=Geofencing, 3=Weather, 4=Audience Proximity';
COMMENT ON COLUMN public.campaigns.latitude IS 'Latitude for geofencing trigger (decimal degrees)';
COMMENT ON COLUMN public.campaigns.longitude IS 'Longitude for geofencing trigger (decimal degrees)';
COMMENT ON COLUMN public.campaigns.radius IS 'Radius for geofencing trigger (meters)';
COMMENT ON COLUMN public.campaigns.min_temp IS 'Minimum temperature for weather trigger';
COMMENT ON COLUMN public.campaigns.max_temp IS 'Maximum temperature for weather trigger';
COMMENT ON COLUMN public.campaigns.units IS 'Temperature units: standard, metric, or imperial';
COMMENT ON COLUMN public.campaigns.distance IS 'Distance for audience proximity trigger (meters)';
