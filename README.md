# Digital Signage Manager

A comprehensive digital signage content management system built with React, Express, and Supabase.

## Quick Start

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- A Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd DigitalSignageManager
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase credentials**

   ⚠️ **IMPORTANT**: You must configure Supabase credentials before running the application.

   - Copy the environment template:
     ```bash
     cp .env.example .env
     ```

   - Follow the detailed setup guide: [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)

4. **Start the application**

   For development:
   ```bash
   npm run dev
   ```

   For production:
   ```bash
   npm run build
   npm start
   ```

5. **Access the application**

   Open your browser and navigate to `http://localhost:5000`

## Features

- **User Management**: Authentication and user profiles
- **Team Management**: Multi-tenant team support
- **Media Library**: Upload and manage media files
- **Screen Management**: Configure and monitor digital screens
- **Campaign Management**: Create and schedule content campaigns
- **Slide Designer**: Visual editor for creating custom slides
- **Real-time Updates**: Live content synchronization

## Project Structure

```
├── client/          # React frontend application
├── server/          # Express backend API
├── shared/          # Shared types and schemas
├── dist/            # Built application files
└── supabase_*.sql   # Database setup scripts
```

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run check` - Type checking
- `npm run db:push` - Push database schema changes

## Environment Variables

Required environment variables (see `.env.example`):

- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anon/public key
- `DATABASE_URL` - PostgreSQL connection string (optional)

## Troubleshooting

### "Missing Supabase credentials" Error

This error occurs when the required Supabase environment variables are not set. Follow these steps:

1. Ensure you have a `.env` file in the project root
2. Check that `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` are set
3. Verify the values are correct (no extra spaces or quotes)
4. After updating `.env`, rebuild the application: `npm run build`
5. See [SUPABASE_SETUP.md](./SUPABASE_SETUP.md) for detailed instructions

**Note**: If you update your `.env` file, you must rebuild the application for production mode (`npm run build`) as the frontend is statically built with the environment variables.

### Application Won't Start

1. Check that all dependencies are installed: `npm install`
2. Verify Node.js version: `node --version` (should be v18+)
3. Ensure port 5000 is available
4. Check the console for specific error messages

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Enhance useScreen Hook

1. We need to fetch following fields from the database for each screen;
   - "health" field from the "screens" table
   - MAX value of "file_download_count" and MAX value of "total_file_download" fields from the "screen_activities" table for the selected screen
   - "trial_ends_at", "subscription_status", "lemonslemonsqueezy_subscription_id" and "billing_cycle" fields from the "screen_registrations" table

2. If the "subscription_status" is "trial";
   - Show a "Trial" badge in red color on the screen card next to the "Online" or "Offline" badge
   - If the "trial_ends_at" is in the past;
      - disable "Configure" and "Campaigns" buttons on the screen card
      - disable "Screen Options" --> "Configure" menu.

## Screen Details Modal Window
1. Click on the "View Screen" menu to launch the modal window.
2. The modal window will display the screen details.
3. You can close the modal window by clicking on the "Close" button.
4. The modal window title will display the screen name & code.
5. There will be 2 tabs in the modal window:
   - "Details": This tab will display the screen details.
   - "Subscriptions": This tab will display the subscriptions associated with the screen.
6. "Details" tab will display the following information:
   - On the Left side of the tab, read the 'health' field of the screen (already fetched via useScreen hook) and display the details as labels. The 'health' field is a json field and contains the following information;
     
     Android Device Data:
     
     - App Version: 0.1.0
     - Build Number: 1
     - Package Name: com.app.signage
     - Memory Total: 7901040
     - Memory Free: 229696
     - Memory Available: 2996748
     - Memory Used: 7671344
     - Memory Usage Percentage: 97.09
     - Internal Storage Total: 111G
     - Internal Storage Used: 67G
     - Internal Storage Available: 44G
     - Internal Storage Usage Percentage: 61
     - External Storage Total: 111G
     - External Storage Used: 68G
     - External Storage Available: 44G

     Desktop Device Data Sample;

      - App Version: 0.1.0
      - Build Number: 1
      - Package Name: com.app.signage
      - CPU Usage Percentage: 26.99
      - Memory Total: 7517436
      - Memory Free: 254892
      - Memory Used: 7262544
      - Memory Usage Percentage: 96.61
      - Disk Filesystem: /dev/sda1
      - Disk Total: 121399984
      - Disk Used: 49022120
      - Disk Available: 66164900
      - Disk Usage Percentage: 43
      - Platform: Linux

      Show these information in some meaningful way.

   - On the Right side of the tab;
      - Header should be "File Download Progress"
      - read the value of 'file_download_count' and 'total_file_download' retrieved via useScreen hookfor the selected screen and display the information as a circular progressbar showing file download progress.
      - Inside the circular progressbar, show the "file_download_count" of "total_file_download" values as labels. i.e. 10 of 100

7. "Subscriptions" tab
   - The "Subscriptions" tab will display the subscriptions associated with the screen.
   - You can add a new subscription by clicking on the "Add Subscription" button.

## Campaign Enhancements

1. The Campaign can be displayed based on following trigger types;
   - 1 - Default Loop
   - 2 - Geofencing (store latitude and longitude and radius in campaigns table)
   - 3 - Weather (store min_temp, max_temp and units in campaigns table)
   - 4 - Audiance Proximity (store distance in campaigns table). This will override the default loop, Geofencing and Weather trigger types and display the campaign when the user is within the specified distance from the screen.
2. I have already added following fields to the campaigns table in the supabase database;
   - trigger_type int4 NOT NULL DEFAULT 1 (default to 1)
   - latitude float8 (default to null)
   - longitude float8 (default to null)
   - radius float8 (default to null)
   - min_temp float8 (default to null)
   - max_temp float8 (default to null)
   - units text (default to null) - can be 'standard', 'metric', 'imperial'
   - distance float8 (default to null)
3. In the Campaing Editor, under the existing "Details" tab, add following fields;
   - Trigger Type (dropdown with 4 options as listed above)
   - For "Geofencing" trigger type, show 3 fields;
      - Latitude
      - Longitude
      - Radius
   - For "Weather" trigger type, show 3 fields;
      - Min Temp
      - Max Temp
      - Units (dropdown with 3 options - 'standard', 'metric', 'imperial')
      - Show a tooltip on "Units" field to explain what are the 3 unit types. i.e. on click of tooltip icon, show a popover with informaton that,
        - Standard Units - Fahrenheit for temperature and Inches for precipitation
        - Metric Units - Celsius for temperature and Millimeters for precipitation
        - Imperial Units - Fahrenheit for temperature and Inches for precipitation
   - For "Audiance Proximity" trigger type, show 1 field;
      - Distance (in meters)
      - Show a tooltip on "Distance" field to explain what is the recommended value. i.e. on click of tooltip icon, show a popover with informaton that,
        - Recommended value is 2 meters
        - Min value is 5 meters
        - Max value is 1 meters
   - For "Default Loop" trigger type, do not show any fields.
   - Show a tooltip on "Trigger Type" field to explain what are the 4 trigger types. i.e. on click of tooltip icon, show a popover with informaton that,
      - Default Loop - Campaign will be displayed in a loop
      - Geofencing - Campaign will be displayed when the screen location is within the specified radius of the latitude and longitude. It will display in the order it is added to the screen campaings list.
      - Weather - Campaign will be displayed when the screen location's temperature is within the specified range. It will display in the order it is added to the screen campaings list.
      - Audiance Proximity - Campaign will be displayed when the user is within the specified distance from the screen. It will override the Default Loop, Geofencing and Weather trigger types.
   - Save/Update the newly added fields in the database.


## License

MIT License - see LICENSE file for details
